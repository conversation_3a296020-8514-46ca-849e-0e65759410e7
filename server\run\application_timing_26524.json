[{"name": "Process Start", "start": 1748263903895, "end": 1748263907120, "duration": 3225, "pid": 26524, "index": 0}, {"name": "Application Start", "start": 1748263907122, "end": 1748263909585, "duration": 2463, "pid": 26524, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748263907152, "end": 1748263907196, "duration": 44, "pid": 26524, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748263907196, "end": 1748263907251, "duration": 55, "pid": 26524, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748263907197, "end": 1748263907199, "duration": 2, "pid": 26524, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748263907202, "end": 1748263907203, "duration": 1, "pid": 26524, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748263907204, "end": 1748263907205, "duration": 1, "pid": 26524, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748263907206, "end": 1748263907206, "duration": 0, "pid": 26524, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748263907209, "end": 1748263907210, "duration": 1, "pid": 26524, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748263907211, "end": 1748263907212, "duration": 1, "pid": 26524, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748263907213, "end": 1748263907214, "duration": 1, "pid": 26524, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748263907215, "end": 1748263907216, "duration": 1, "pid": 26524, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748263907217, "end": 1748263907218, "duration": 1, "pid": 26524, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748263907221, "end": 1748263907222, "duration": 1, "pid": 26524, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748263907223, "end": 1748263907223, "duration": 0, "pid": 26524, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748263907224, "end": 1748263907225, "duration": 1, "pid": 26524, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748263907226, "end": 1748263907227, "duration": 1, "pid": 26524, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748263907228, "end": 1748263907228, "duration": 0, "pid": 26524, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748263907229, "end": 1748263907230, "duration": 1, "pid": 26524, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748263907231, "end": 1748263907232, "duration": 1, "pid": 26524, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748263907233, "end": 1748263907233, "duration": 0, "pid": 26524, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748263907234, "end": 1748263907235, "duration": 1, "pid": 26524, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748263907236, "end": 1748263907236, "duration": 0, "pid": 26524, "index": 22}, {"name": "Require(19) node_modules/egg/config/config.default.js", "start": 1748263907238, "end": 1748263907238, "duration": 0, "pid": 26524, "index": 23}, {"name": "Require(20) config/config.default.js", "start": 1748263907240, "end": 1748263907240, "duration": 0, "pid": 26524, "index": 24}, {"name": "Require(21) node_modules/egg-security/config/config.local.js", "start": 1748263907241, "end": 1748263907241, "duration": 0, "pid": 26524, "index": 25}, {"name": "Require(22) node_modules/egg-watcher/config/config.local.js", "start": 1748263907243, "end": 1748263907243, "duration": 0, "pid": 26524, "index": 26}, {"name": "Require(23) node_modules/egg-view/config/config.local.js", "start": 1748263907246, "end": 1748263907247, "duration": 1, "pid": 26524, "index": 27}, {"name": "Require(24) node_modules/egg/config/config.local.js", "start": 1748263907250, "end": 1748263907251, "duration": 1, "pid": 26524, "index": 28}, {"name": "Require(25) config/config.local.js", "start": 1748263907251, "end": 1748263907251, "duration": 0, "pid": 26524, "index": 29}, {"name": "Load extend/application.js", "start": 1748263907252, "end": 1748263907391, "duration": 139, "pid": 26524, "index": 30}, {"name": "Require(26) node_modules/egg-session/app/extend/application.js", "start": 1748263907253, "end": 1748263907254, "duration": 1, "pid": 26524, "index": 31}, {"name": "Require(27) node_modules/egg-security/app/extend/application.js", "start": 1748263907255, "end": 1748263907256, "duration": 1, "pid": 26524, "index": 32}, {"name": "Require(28) node_modules/egg-jsonp/app/extend/application.js", "start": 1748263907257, "end": 1748263907263, "duration": 6, "pid": 26524, "index": 33}, {"name": "Require(29) node_modules/egg-schedule/app/extend/application.js", "start": 1748263907266, "end": 1748263907274, "duration": 8, "pid": 26524, "index": 34}, {"name": "Require(30) node_modules/egg-logrotator/app/extend/application.js", "start": 1748263907276, "end": 1748263907279, "duration": 3, "pid": 26524, "index": 35}, {"name": "Require(31) node_modules/egg-view/app/extend/application.js", "start": 1748263907280, "end": 1748263907286, "duration": 6, "pid": 26524, "index": 36}, {"name": "Require(32) node_modules/egg-jwt/app/extend/application.js", "start": 1748263907288, "end": 1748263907382, "duration": 94, "pid": 26524, "index": 37}, {"name": "Load extend/request.js", "start": 1748263907391, "end": 1748263907413, "duration": 22, "pid": 26524, "index": 38}, {"name": "Require(33) node_modules/egg/app/extend/request.js", "start": 1748263907402, "end": 1748263907404, "duration": 2, "pid": 26524, "index": 39}, {"name": "Load extend/response.js", "start": 1748263907413, "end": 1748263907439, "duration": 26, "pid": 26524, "index": 40}, {"name": "Require(34) node_modules/egg/app/extend/response.js", "start": 1748263907425, "end": 1748263907431, "duration": 6, "pid": 26524, "index": 41}, {"name": "Load extend/context.js", "start": 1748263907439, "end": 1748263907528, "duration": 89, "pid": 26524, "index": 42}, {"name": "Require(35) node_modules/egg-security/app/extend/context.js", "start": 1748263907441, "end": 1748263907466, "duration": 25, "pid": 26524, "index": 43}, {"name": "Require(36) node_modules/egg-jsonp/app/extend/context.js", "start": 1748263907467, "end": 1748263907470, "duration": 3, "pid": 26524, "index": 44}, {"name": "Require(37) node_modules/egg-i18n/app/extend/context.js", "start": 1748263907471, "end": 1748263907472, "duration": 1, "pid": 26524, "index": 45}, {"name": "Require(38) node_modules/egg-multipart/app/extend/context.js", "start": 1748263907473, "end": 1748263907509, "duration": 36, "pid": 26524, "index": 46}, {"name": "Require(39) node_modules/egg-view/app/extend/context.js", "start": 1748263907510, "end": 1748263907512, "duration": 2, "pid": 26524, "index": 47}, {"name": "Require(40) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748263907515, "end": 1748263907516, "duration": 1, "pid": 26524, "index": 48}, {"name": "Require(41) node_modules/egg/app/extend/context.js", "start": 1748263907517, "end": 1748263907521, "duration": 4, "pid": 26524, "index": 49}, {"name": "Load extend/helper.js", "start": 1748263907528, "end": 1748263907581, "duration": 53, "pid": 26524, "index": 50}, {"name": "Require(42) node_modules/egg-security/app/extend/helper.js", "start": 1748263907529, "end": 1748263907562, "duration": 33, "pid": 26524, "index": 51}, {"name": "Require(43) node_modules/egg/app/extend/helper.js", "start": 1748263907571, "end": 1748263907572, "duration": 1, "pid": 26524, "index": 52}, {"name": "Require(44) app/extend/helper.js", "start": 1748263907572, "end": 1748263907573, "duration": 1, "pid": 26524, "index": 53}, {"name": "Load app.js", "start": 1748263907582, "end": 1748263907711, "duration": 129, "pid": 26524, "index": 54}, {"name": "Require(45) node_modules/egg-session/app.js", "start": 1748263907582, "end": 1748263907583, "duration": 1, "pid": 26524, "index": 55}, {"name": "Require(46) node_modules/egg-security/app.js", "start": 1748263907584, "end": 1748263907586, "duration": 2, "pid": 26524, "index": 56}, {"name": "Require(47) node_modules/egg-onerror/app.js", "start": 1748263907587, "end": 1748263907605, "duration": 18, "pid": 26524, "index": 57}, {"name": "Require(48) node_modules/egg-i18n/app.js", "start": 1748263907606, "end": 1748263907627, "duration": 21, "pid": 26524, "index": 58}, {"name": "Require(49) node_modules/egg-watcher/app.js", "start": 1748263907628, "end": 1748263907649, "duration": 21, "pid": 26524, "index": 59}, {"name": "Require(50) node_modules/egg-schedule/app.js", "start": 1748263907650, "end": 1748263907652, "duration": 2, "pid": 26524, "index": 60}, {"name": "Require(51) node_modules/egg-multipart/app.js", "start": 1748263907652, "end": 1748263907654, "duration": 2, "pid": 26524, "index": 61}, {"name": "Require(52) node_modules/egg-development/app.js", "start": 1748263907655, "end": 1748263907655, "duration": 0, "pid": 26524, "index": 62}, {"name": "Require(53) node_modules/egg-logrotator/app.js", "start": 1748263907656, "end": 1748263907656, "duration": 0, "pid": 26524, "index": 63}, {"name": "Require(54) node_modules/egg-static/app.js", "start": 1748263907657, "end": 1748263907657, "duration": 0, "pid": 26524, "index": 64}, {"name": "Require(55) node_modules/egg-sequelize/app.js", "start": 1748263907659, "end": 1748263907659, "duration": 0, "pid": 26524, "index": 65}, {"name": "Require(56) node_modules/egg-jwt/app.js", "start": 1748263907660, "end": 1748263907661, "duration": 1, "pid": 26524, "index": 66}, {"name": "Require(57) node_modules/egg-cors/app.js", "start": 1748263907661, "end": 1748263907662, "duration": 1, "pid": 26524, "index": 67}, {"name": "Require(58) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748263907664, "end": 1748263907669, "duration": 5, "pid": 26524, "index": 68}, {"name": "Require(59) node_modules/egg-mysql/app.js", "start": 1748263907669, "end": 1748263907708, "duration": 39, "pid": 26524, "index": 69}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748263907729, "end": 1748263909559, "duration": 1830, "pid": 26524, "index": 70}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748263908707, "end": 1748263908850, "duration": 143, "pid": 26524, "index": 71}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748263908738, "end": 1748263909472, "duration": 734, "pid": 26524, "index": 72}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748263908892, "end": 1748263909584, "duration": 692, "pid": 26524, "index": 73}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748263908997, "end": 1748263909553, "duration": 556, "pid": 26524, "index": 74}, {"name": "Load Service", "start": 1748263908997, "end": 1748263909151, "duration": 154, "pid": 26524, "index": 75}, {"name": "Load \"service\" to Context", "start": 1748263908997, "end": 1748263909151, "duration": 154, "pid": 26524, "index": 76}, {"name": "Load Middleware", "start": 1748263909151, "end": 1748263909327, "duration": 176, "pid": 26524, "index": 77}, {"name": "Load \"middlewares\" to Application", "start": 1748263909151, "end": 1748263909311, "duration": 160, "pid": 26524, "index": 78}, {"name": "Load Controller", "start": 1748263909327, "end": 1748263909382, "duration": 55, "pid": 26524, "index": 79}, {"name": "Load \"controller\" to Application", "start": 1748263909328, "end": 1748263909382, "duration": 54, "pid": 26524, "index": 80}, {"name": "Load Router", "start": 1748263909382, "end": 1748263909389, "duration": 7, "pid": 26524, "index": 81}, {"name": "Require(60) app/router.js", "start": 1748263909383, "end": 1748263909384, "duration": 1, "pid": 26524, "index": 82}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748263909385, "end": 1748263909472, "duration": 87, "pid": 26524, "index": 83}]