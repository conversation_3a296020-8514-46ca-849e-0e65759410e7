[{"name": "Process Start", "start": 1748263898714, "end": 1748263902543, "duration": 3829, "pid": 30004, "index": 0}, {"name": "Application Start", "start": 1748263902546, "end": 1748263906518, "duration": 3972, "pid": 30004, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748263902597, "end": 1748263902696, "duration": 99, "pid": 30004, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748263902696, "end": 1748263902805, "duration": 109, "pid": 30004, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748263902706, "end": 1748263902707, "duration": 1, "pid": 30004, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748263902712, "end": 1748263902712, "duration": 0, "pid": 30004, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748263902716, "end": 1748263902718, "duration": 2, "pid": 30004, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748263902720, "end": 1748263902721, "duration": 1, "pid": 30004, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748263902723, "end": 1748263902725, "duration": 2, "pid": 30004, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748263902727, "end": 1748263902728, "duration": 1, "pid": 30004, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748263902729, "end": 1748263902730, "duration": 1, "pid": 30004, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748263902737, "end": 1748263902738, "duration": 1, "pid": 30004, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748263902743, "end": 1748263902744, "duration": 1, "pid": 30004, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748263902751, "end": 1748263902753, "duration": 2, "pid": 30004, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748263902756, "end": 1748263902759, "duration": 3, "pid": 30004, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748263902762, "end": 1748263902763, "duration": 1, "pid": 30004, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748263902767, "end": 1748263902769, "duration": 2, "pid": 30004, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748263902772, "end": 1748263902774, "duration": 2, "pid": 30004, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748263902776, "end": 1748263902777, "duration": 1, "pid": 30004, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748263902778, "end": 1748263902779, "duration": 1, "pid": 30004, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748263902780, "end": 1748263902781, "duration": 1, "pid": 30004, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748263902782, "end": 1748263902783, "duration": 1, "pid": 30004, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748263902784, "end": 1748263902784, "duration": 0, "pid": 30004, "index": 22}, {"name": "Require(19) node_modules/egg/config/config.default.js", "start": 1748263902785, "end": 1748263902786, "duration": 1, "pid": 30004, "index": 23}, {"name": "Require(20) config/config.default.js", "start": 1748263902788, "end": 1748263902788, "duration": 0, "pid": 30004, "index": 24}, {"name": "Require(21) node_modules/egg-security/config/config.local.js", "start": 1748263902791, "end": 1748263902791, "duration": 0, "pid": 30004, "index": 25}, {"name": "Require(22) node_modules/egg-watcher/config/config.local.js", "start": 1748263902793, "end": 1748263902794, "duration": 1, "pid": 30004, "index": 26}, {"name": "Require(23) node_modules/egg-view/config/config.local.js", "start": 1748263902800, "end": 1748263902800, "duration": 0, "pid": 30004, "index": 27}, {"name": "Require(24) node_modules/egg/config/config.local.js", "start": 1748263902804, "end": 1748263902805, "duration": 1, "pid": 30004, "index": 28}, {"name": "Require(25) config/config.local.js", "start": 1748263902805, "end": 1748263902805, "duration": 0, "pid": 30004, "index": 29}, {"name": "Load extend/application.js", "start": 1748263902808, "end": 1748263903055, "duration": 247, "pid": 30004, "index": 30}, {"name": "Require(26) node_modules/egg-session/app/extend/application.js", "start": 1748263902810, "end": 1748263902811, "duration": 1, "pid": 30004, "index": 31}, {"name": "Require(27) node_modules/egg-security/app/extend/application.js", "start": 1748263902812, "end": 1748263902817, "duration": 5, "pid": 30004, "index": 32}, {"name": "Require(28) node_modules/egg-jsonp/app/extend/application.js", "start": 1748263902819, "end": 1748263902848, "duration": 29, "pid": 30004, "index": 33}, {"name": "Require(29) node_modules/egg-schedule/app/extend/application.js", "start": 1748263902852, "end": 1748263902862, "duration": 10, "pid": 30004, "index": 34}, {"name": "Require(30) node_modules/egg-logrotator/app/extend/application.js", "start": 1748263902863, "end": 1748263902868, "duration": 5, "pid": 30004, "index": 35}, {"name": "Require(31) node_modules/egg-view/app/extend/application.js", "start": 1748263902869, "end": 1748263902874, "duration": 5, "pid": 30004, "index": 36}, {"name": "Require(32) node_modules/egg-jwt/app/extend/application.js", "start": 1748263902876, "end": 1748263903039, "duration": 163, "pid": 30004, "index": 37}, {"name": "Load extend/request.js", "start": 1748263903055, "end": 1748263903080, "duration": 25, "pid": 30004, "index": 38}, {"name": "Require(33) node_modules/egg/app/extend/request.js", "start": 1748263903067, "end": 1748263903070, "duration": 3, "pid": 30004, "index": 39}, {"name": "Load extend/response.js", "start": 1748263903080, "end": 1748263903106, "duration": 26, "pid": 30004, "index": 40}, {"name": "Require(34) node_modules/egg/app/extend/response.js", "start": 1748263903092, "end": 1748263903096, "duration": 4, "pid": 30004, "index": 41}, {"name": "Load extend/context.js", "start": 1748263903106, "end": 1748263903194, "duration": 88, "pid": 30004, "index": 42}, {"name": "Require(35) node_modules/egg-security/app/extend/context.js", "start": 1748263903108, "end": 1748263903129, "duration": 21, "pid": 30004, "index": 43}, {"name": "Require(36) node_modules/egg-jsonp/app/extend/context.js", "start": 1748263903131, "end": 1748263903134, "duration": 3, "pid": 30004, "index": 44}, {"name": "Require(37) node_modules/egg-i18n/app/extend/context.js", "start": 1748263903135, "end": 1748263903136, "duration": 1, "pid": 30004, "index": 45}, {"name": "Require(38) node_modules/egg-multipart/app/extend/context.js", "start": 1748263903137, "end": 1748263903175, "duration": 38, "pid": 30004, "index": 46}, {"name": "Require(39) node_modules/egg-view/app/extend/context.js", "start": 1748263903178, "end": 1748263903179, "duration": 1, "pid": 30004, "index": 47}, {"name": "Require(40) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748263903182, "end": 1748263903183, "duration": 1, "pid": 30004, "index": 48}, {"name": "Require(41) node_modules/egg/app/extend/context.js", "start": 1748263903184, "end": 1748263903187, "duration": 3, "pid": 30004, "index": 49}, {"name": "Load extend/helper.js", "start": 1748263903194, "end": 1748263903257, "duration": 63, "pid": 30004, "index": 50}, {"name": "Require(42) node_modules/egg-security/app/extend/helper.js", "start": 1748263903195, "end": 1748263903238, "duration": 43, "pid": 30004, "index": 51}, {"name": "Require(43) node_modules/egg/app/extend/helper.js", "start": 1748263903244, "end": 1748263903245, "duration": 1, "pid": 30004, "index": 52}, {"name": "Require(44) app/extend/helper.js", "start": 1748263903246, "end": 1748263903249, "duration": 3, "pid": 30004, "index": 53}, {"name": "Load app.js", "start": 1748263903258, "end": 1748263903456, "duration": 198, "pid": 30004, "index": 54}, {"name": "Require(45) node_modules/egg-session/app.js", "start": 1748263903259, "end": 1748263903278, "duration": 19, "pid": 30004, "index": 55}, {"name": "Require(46) node_modules/egg-security/app.js", "start": 1748263903279, "end": 1748263903286, "duration": 7, "pid": 30004, "index": 56}, {"name": "Require(47) node_modules/egg-onerror/app.js", "start": 1748263903287, "end": 1748263903315, "duration": 28, "pid": 30004, "index": 57}, {"name": "Require(48) node_modules/egg-i18n/app.js", "start": 1748263903316, "end": 1748263903344, "duration": 28, "pid": 30004, "index": 58}, {"name": "Require(49) node_modules/egg-watcher/app.js", "start": 1748263903345, "end": 1748263903385, "duration": 40, "pid": 30004, "index": 59}, {"name": "Require(50) node_modules/egg-schedule/app.js", "start": 1748263903386, "end": 1748263903387, "duration": 1, "pid": 30004, "index": 60}, {"name": "Require(51) node_modules/egg-multipart/app.js", "start": 1748263903389, "end": 1748263903392, "duration": 3, "pid": 30004, "index": 61}, {"name": "Require(52) node_modules/egg-development/app.js", "start": 1748263903393, "end": 1748263903394, "duration": 1, "pid": 30004, "index": 62}, {"name": "Require(53) node_modules/egg-logrotator/app.js", "start": 1748263903394, "end": 1748263903395, "duration": 1, "pid": 30004, "index": 63}, {"name": "Require(54) node_modules/egg-static/app.js", "start": 1748263903395, "end": 1748263903396, "duration": 1, "pid": 30004, "index": 64}, {"name": "Require(55) node_modules/egg-sequelize/app.js", "start": 1748263903399, "end": 1748263903400, "duration": 1, "pid": 30004, "index": 65}, {"name": "Require(56) node_modules/egg-jwt/app.js", "start": 1748263903400, "end": 1748263903401, "duration": 1, "pid": 30004, "index": 66}, {"name": "Require(57) node_modules/egg-cors/app.js", "start": 1748263903402, "end": 1748263903402, "duration": 0, "pid": 30004, "index": 67}, {"name": "Require(58) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748263903403, "end": 1748263903409, "duration": 6, "pid": 30004, "index": 68}, {"name": "Require(59) node_modules/egg-mysql/app.js", "start": 1748263903409, "end": 1748263903454, "duration": 45, "pid": 30004, "index": 69}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748263903494, "end": 1748263906507, "duration": 3013, "pid": 30004, "index": 70}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748263904811, "end": 1748263904933, "duration": 122, "pid": 30004, "index": 71}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748263904845, "end": 1748263906378, "duration": 1533, "pid": 30004, "index": 72}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748263905017, "end": 1748263906517, "duration": 1500, "pid": 30004, "index": 73}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748263905201, "end": 1748263906472, "duration": 1271, "pid": 30004, "index": 74}, {"name": "Load Service", "start": 1748263905201, "end": 1748263905556, "duration": 355, "pid": 30004, "index": 75}, {"name": "Load \"service\" to Context", "start": 1748263905202, "end": 1748263905556, "duration": 354, "pid": 30004, "index": 76}, {"name": "Load Middleware", "start": 1748263905557, "end": 1748263905898, "duration": 341, "pid": 30004, "index": 77}, {"name": "Load \"middlewares\" to Application", "start": 1748263905557, "end": 1748263905870, "duration": 313, "pid": 30004, "index": 78}, {"name": "Load Controller", "start": 1748263905898, "end": 1748263906225, "duration": 327, "pid": 30004, "index": 79}, {"name": "Load \"controller\" to Application", "start": 1748263905898, "end": 1748263906225, "duration": 327, "pid": 30004, "index": 80}, {"name": "Load Router", "start": 1748263906225, "end": 1748263906239, "duration": 14, "pid": 30004, "index": 81}, {"name": "Require(60) app/router.js", "start": 1748263906226, "end": 1748263906228, "duration": 2, "pid": 30004, "index": 82}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748263906230, "end": 1748263906378, "duration": 148, "pid": 30004, "index": 83}]