{"name": "stock-analysis-web", "version": "0.0.0", "private": true, "type": "module", "scripts": {"serve": "vite", "dev": "cd server && npm run dev", "build": "run-p type-check build-only", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build --skip<PERSON><PERSON><PERSON><PERSON><PERSON>", "format": "prettier --write src/", "proxy": "node proxy-server.cjs", "start": "run-p serve proxy", "test": "vitest run src/tests", "test:watch": "vitest src/tests", "test:coverage": "vitest run src/tests --coverage", "test:ui": "vitest --ui src/tests", "test:server": "cd server && npm test"}, "dependencies": {"axios": "^1.9.0", "echarts": "^5.4.3", "egg-mysql": "^5.0.0", "egg-redis": "^2.6.1", "egg-validate": "^2.0.2", "element-plus": "^2.9.8", "file-saver": "^2.0.5", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "mitt": "^3.0.1", "node-fetch": "^3.3.2", "pinia": "^2.1.6", "uuid": "^11.1.0", "vue": "^3.3.4", "vue-router": "^4.2.4", "xlsx": "^0.18.5", "yarn": "^1.22.19"}, "devDependencies": {"@tsconfig/node16": "^1.0.3", "@types/file-saver": "^2.0.7", "@types/node": "^16.18.0", "@types/uuid": "^10.0.0", "@vitejs/plugin-vue": "^4.2.3", "@vitejs/plugin-vue-jsx": "^3.0.1", "@vitest/coverage-c8": "^0.33.0", "@vitest/ui": "^0.34.6", "@vue/test-utils": "^2.4.1", "@vue/tsconfig": "^0.4.0", "body-parser": "^2.2.0", "cors": "^2.8.5", "express": "^5.1.0", "jsdom": "^22.1.0", "jsonwebtoken": "^9.0.2", "npm-run-all": "^4.1.5", "prettier": "^2.8.8", "sequelize-cli": "^6.6.2", "typescript": "~5.0.4", "vite": "^4.3.9", "vite-plugin-vue-devtools": "^1.0.0-rc.5", "vitest": "^0.34.6", "vue-tsc": "^1.8.5"}}