[{"name": "Process Start", "start": 1748263910087, "end": 1748263913206, "duration": 3119, "pid": 30736, "index": 0}, {"name": "Application Start", "start": 1748263913212, "end": 1748263915891, "duration": 2679, "pid": 30736, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748263913237, "end": 1748263913276, "duration": 39, "pid": 30736, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748263913276, "end": 1748263913338, "duration": 62, "pid": 30736, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748263913277, "end": 1748263913280, "duration": 3, "pid": 30736, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748263913286, "end": 1748263913286, "duration": 0, "pid": 30736, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748263913288, "end": 1748263913289, "duration": 1, "pid": 30736, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748263913290, "end": 1748263913290, "duration": 0, "pid": 30736, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748263913292, "end": 1748263913293, "duration": 1, "pid": 30736, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748263913293, "end": 1748263913294, "duration": 1, "pid": 30736, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748263913295, "end": 1748263913296, "duration": 1, "pid": 30736, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748263913298, "end": 1748263913299, "duration": 1, "pid": 30736, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748263913300, "end": 1748263913301, "duration": 1, "pid": 30736, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748263913302, "end": 1748263913304, "duration": 2, "pid": 30736, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748263913305, "end": 1748263913306, "duration": 1, "pid": 30736, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748263913306, "end": 1748263913307, "duration": 1, "pid": 30736, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748263913308, "end": 1748263913309, "duration": 1, "pid": 30736, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748263913309, "end": 1748263913310, "duration": 1, "pid": 30736, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748263913311, "end": 1748263913313, "duration": 2, "pid": 30736, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748263913315, "end": 1748263913315, "duration": 0, "pid": 30736, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748263913316, "end": 1748263913317, "duration": 1, "pid": 30736, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748263913318, "end": 1748263913318, "duration": 0, "pid": 30736, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748263913319, "end": 1748263913320, "duration": 1, "pid": 30736, "index": 22}, {"name": "Require(19) node_modules/egg/config/config.default.js", "start": 1748263913320, "end": 1748263913321, "duration": 1, "pid": 30736, "index": 23}, {"name": "Require(20) config/config.default.js", "start": 1748263913322, "end": 1748263913322, "duration": 0, "pid": 30736, "index": 24}, {"name": "Require(21) node_modules/egg-security/config/config.local.js", "start": 1748263913324, "end": 1748263913324, "duration": 0, "pid": 30736, "index": 25}, {"name": "Require(22) node_modules/egg-watcher/config/config.local.js", "start": 1748263913326, "end": 1748263913326, "duration": 0, "pid": 30736, "index": 26}, {"name": "Require(23) node_modules/egg-view/config/config.local.js", "start": 1748263913332, "end": 1748263913333, "duration": 1, "pid": 30736, "index": 27}, {"name": "Require(24) node_modules/egg/config/config.local.js", "start": 1748263913337, "end": 1748263913338, "duration": 1, "pid": 30736, "index": 28}, {"name": "Require(25) config/config.local.js", "start": 1748263913338, "end": 1748263913338, "duration": 0, "pid": 30736, "index": 29}, {"name": "Load extend/application.js", "start": 1748263913340, "end": 1748263913491, "duration": 151, "pid": 30736, "index": 30}, {"name": "Require(26) node_modules/egg-session/app/extend/application.js", "start": 1748263913341, "end": 1748263913342, "duration": 1, "pid": 30736, "index": 31}, {"name": "Require(27) node_modules/egg-security/app/extend/application.js", "start": 1748263913343, "end": 1748263913348, "duration": 5, "pid": 30736, "index": 32}, {"name": "Require(28) node_modules/egg-jsonp/app/extend/application.js", "start": 1748263913349, "end": 1748263913357, "duration": 8, "pid": 30736, "index": 33}, {"name": "Require(29) node_modules/egg-schedule/app/extend/application.js", "start": 1748263913359, "end": 1748263913371, "duration": 12, "pid": 30736, "index": 34}, {"name": "Require(30) node_modules/egg-logrotator/app/extend/application.js", "start": 1748263913373, "end": 1748263913375, "duration": 2, "pid": 30736, "index": 35}, {"name": "Require(31) node_modules/egg-view/app/extend/application.js", "start": 1748263913376, "end": 1748263913380, "duration": 4, "pid": 30736, "index": 36}, {"name": "Require(32) node_modules/egg-jwt/app/extend/application.js", "start": 1748263913382, "end": 1748263913481, "duration": 99, "pid": 30736, "index": 37}, {"name": "Load extend/request.js", "start": 1748263913491, "end": 1748263913521, "duration": 30, "pid": 30736, "index": 38}, {"name": "Require(33) node_modules/egg/app/extend/request.js", "start": 1748263913504, "end": 1748263913506, "duration": 2, "pid": 30736, "index": 39}, {"name": "Load extend/response.js", "start": 1748263913521, "end": 1748263913553, "duration": 32, "pid": 30736, "index": 40}, {"name": "Require(34) node_modules/egg/app/extend/response.js", "start": 1748263913535, "end": 1748263913540, "duration": 5, "pid": 30736, "index": 41}, {"name": "Load extend/context.js", "start": 1748263913553, "end": 1748263913656, "duration": 103, "pid": 30736, "index": 42}, {"name": "Require(35) node_modules/egg-security/app/extend/context.js", "start": 1748263913554, "end": 1748263913581, "duration": 27, "pid": 30736, "index": 43}, {"name": "Require(36) node_modules/egg-jsonp/app/extend/context.js", "start": 1748263913583, "end": 1748263913586, "duration": 3, "pid": 30736, "index": 44}, {"name": "Require(37) node_modules/egg-i18n/app/extend/context.js", "start": 1748263913588, "end": 1748263913589, "duration": 1, "pid": 30736, "index": 45}, {"name": "Require(38) node_modules/egg-multipart/app/extend/context.js", "start": 1748263913590, "end": 1748263913630, "duration": 40, "pid": 30736, "index": 46}, {"name": "Require(39) node_modules/egg-view/app/extend/context.js", "start": 1748263913634, "end": 1748263913636, "duration": 2, "pid": 30736, "index": 47}, {"name": "Require(40) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748263913639, "end": 1748263913640, "duration": 1, "pid": 30736, "index": 48}, {"name": "Require(41) node_modules/egg/app/extend/context.js", "start": 1748263913641, "end": 1748263913646, "duration": 5, "pid": 30736, "index": 49}, {"name": "Load extend/helper.js", "start": 1748263913656, "end": 1748263913710, "duration": 54, "pid": 30736, "index": 50}, {"name": "Require(42) node_modules/egg-security/app/extend/helper.js", "start": 1748263913657, "end": 1748263913696, "duration": 39, "pid": 30736, "index": 51}, {"name": "Require(43) node_modules/egg/app/extend/helper.js", "start": 1748263913702, "end": 1748263913702, "duration": 0, "pid": 30736, "index": 52}, {"name": "Require(44) app/extend/helper.js", "start": 1748263913703, "end": 1748263913704, "duration": 1, "pid": 30736, "index": 53}, {"name": "Load app.js", "start": 1748263913711, "end": 1748263913825, "duration": 114, "pid": 30736, "index": 54}, {"name": "Require(45) node_modules/egg-session/app.js", "start": 1748263913712, "end": 1748263913713, "duration": 1, "pid": 30736, "index": 55}, {"name": "Require(46) node_modules/egg-security/app.js", "start": 1748263913714, "end": 1748263913717, "duration": 3, "pid": 30736, "index": 56}, {"name": "Require(47) node_modules/egg-onerror/app.js", "start": 1748263913718, "end": 1748263913734, "duration": 16, "pid": 30736, "index": 57}, {"name": "Require(48) node_modules/egg-i18n/app.js", "start": 1748263913735, "end": 1748263913754, "duration": 19, "pid": 30736, "index": 58}, {"name": "Require(49) node_modules/egg-watcher/app.js", "start": 1748263913755, "end": 1748263913774, "duration": 19, "pid": 30736, "index": 59}, {"name": "Require(50) node_modules/egg-schedule/app.js", "start": 1748263913775, "end": 1748263913776, "duration": 1, "pid": 30736, "index": 60}, {"name": "Require(51) node_modules/egg-multipart/app.js", "start": 1748263913777, "end": 1748263913781, "duration": 4, "pid": 30736, "index": 61}, {"name": "Require(52) node_modules/egg-development/app.js", "start": 1748263913782, "end": 1748263913782, "duration": 0, "pid": 30736, "index": 62}, {"name": "Require(53) node_modules/egg-logrotator/app.js", "start": 1748263913783, "end": 1748263913783, "duration": 0, "pid": 30736, "index": 63}, {"name": "Require(54) node_modules/egg-static/app.js", "start": 1748263913784, "end": 1748263913784, "duration": 0, "pid": 30736, "index": 64}, {"name": "Require(55) node_modules/egg-sequelize/app.js", "start": 1748263913785, "end": 1748263913786, "duration": 1, "pid": 30736, "index": 65}, {"name": "Require(56) node_modules/egg-jwt/app.js", "start": 1748263913786, "end": 1748263913787, "duration": 1, "pid": 30736, "index": 66}, {"name": "Require(57) node_modules/egg-cors/app.js", "start": 1748263913787, "end": 1748263913788, "duration": 1, "pid": 30736, "index": 67}, {"name": "Require(58) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748263913788, "end": 1748263913792, "duration": 4, "pid": 30736, "index": 68}, {"name": "Require(59) node_modules/egg-mysql/app.js", "start": 1748263913792, "end": 1748263913823, "duration": 31, "pid": 30736, "index": 69}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748263913844, "end": 1748263915864, "duration": 2020, "pid": 30736, "index": 70}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748263914688, "end": 1748263914826, "duration": 138, "pid": 30736, "index": 71}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748263914723, "end": 1748263915804, "duration": 1081, "pid": 30736, "index": 72}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748263914936, "end": 1748263915890, "duration": 954, "pid": 30736, "index": 73}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748263915173, "end": 1748263915866, "duration": 693, "pid": 30736, "index": 74}, {"name": "Load Service", "start": 1748263915173, "end": 1748263915366, "duration": 193, "pid": 30736, "index": 75}, {"name": "Load \"service\" to Context", "start": 1748263915173, "end": 1748263915366, "duration": 193, "pid": 30736, "index": 76}, {"name": "Load Middleware", "start": 1748263915366, "end": 1748263915636, "duration": 270, "pid": 30736, "index": 77}, {"name": "Load \"middlewares\" to Application", "start": 1748263915366, "end": 1748263915616, "duration": 250, "pid": 30736, "index": 78}, {"name": "Load Controller", "start": 1748263915636, "end": 1748263915720, "duration": 84, "pid": 30736, "index": 79}, {"name": "Load \"controller\" to Application", "start": 1748263915636, "end": 1748263915720, "duration": 84, "pid": 30736, "index": 80}, {"name": "Load Router", "start": 1748263915720, "end": 1748263915734, "duration": 14, "pid": 30736, "index": 81}, {"name": "Require(60) app/router.js", "start": 1748263915721, "end": 1748263915722, "duration": 1, "pid": 30736, "index": 82}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748263915724, "end": 1748263915804, "duration": 80, "pid": 30736, "index": 83}]